// ignore_for_file: overridden_fields

import 'dart:async';
import 'dart:developer';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/core/utils/mixins/pagination_mixin.dart';
import 'package:logestics/core/utils/mixins/auto_refresh_mixin.dart';
import 'package:logestics/core/utils/snackbar_utils.dart';
import 'package:logestics/features/finance/accounts/repositories/account_repository.dart';
import 'package:logestics/features/finance/expense_categories/repositories/expense_category_repository.dart';
import 'package:logestics/features/finance/expenses/repositories/expense_repository.dart';
import 'package:logestics/features/finance/payees/repositories/payee_repository.dart';
import 'package:logestics/models/finance/account_model.dart';
import 'package:logestics/models/finance/expense_category_model.dart';
import 'package:logestics/models/finance/expense_model.dart';
import 'package:logestics/models/finance/payee_model.dart';
import 'package:logestics/models/finance/chart_of_accounts_model.dart';
import 'package:logestics/features/accounting/chart_of_accounts/repositories/chart_of_accounts_repository.dart';

class ExpenseController extends GetxController
    with PaginationMixin, AutoRefreshMixin {
  final ExpenseRepository expenseRepository;
  final AccountRepository accountRepository;
  final PayeeRepository payeeRepository;
  final ExpenseCategoryRepository categoryRepository;
  final ChartOfAccountsRepository chartOfAccountsRepository;

  // Observable lists
  final expenses = <ExpenseModel>[].obs;
  final filteredExpenses = <ExpenseModel>[].obs;
  final accounts = <AccountModel>[].obs;
  final payees = <PayeeModel>[].obs;
  final categories = <ExpenseCategoryModel>[].obs;
  final chartOfAccounts = <ChartOfAccountsModel>[].obs;

  // Selected items
  final selectedAccount = Rxn<AccountModel>();
  final selectedPayee = Rxn<PayeeModel>();
  final selectedCategory = Rxn<ExpenseCategoryModel>();
  final selectedDate = Rx<DateTime>(DateTime.now());

  // Chart of Accounts selections
  final selectedSourceAccount = Rxn<ChartOfAccountsModel>();
  final selectedDestinationAccount = Rxn<ChartOfAccountsModel>();
  final selectedStartDate =
      Rx<DateTime>(DateTime.now().subtract(const Duration(days: 30)));
  final selectedEndDate = Rx<DateTime>(DateTime.now());

  // Form controllers
  final titleController = TextEditingController();
  final amountController = TextEditingController();
  final referenceController = TextEditingController();
  final notesController = TextEditingController();
  final searchController = TextEditingController();

  // UI state
  final isLoading = false.obs;
  final isDeleting = false.obs;
  final isDrawerOpen = false.obs;
  final showFilter = false.obs;
  @override
  final currentPage = 1.obs;
  @override
  final itemsPerPage = 10.obs;
  final searchQuery = ''.obs;
  final dateFilterType = 'all'.obs;

  // File upload
  final selectedFile = Rx<dynamic>(null);
  final selectedFileBytes = Rx<List<int>?>(null);
  final selectedFileName = ''.obs;
  final isWebFile = false.obs;

  // Form key
  final formKey = GlobalKey<FormState>();

  // Stream subscription for real-time updates
  StreamSubscription<List<ExpenseModel>>? _expensesSubscription;

  ExpenseController({
    required this.expenseRepository,
    required this.accountRepository,
    required this.payeeRepository,
    required this.categoryRepository,
    required this.chartOfAccountsRepository,
  });

  @override
  void onInit() {
    super.onInit();
    searchController.addListener(_onSearchChanged);
    // Set up real-time expense updates
    _setupRealTimeUpdates();
    // The AutoRefreshMixin will handle calling refreshData for other data
  }

  /// Implementation of AutoRefreshMixin.refreshData
  @override
  Future<void> refreshData() async {
    await Future.wait([
      fetchAccounts(),
      fetchPayees(),
      fetchCategories(),
    ]);
    // Expenses are handled by real-time stream, no need to fetch manually
  }

  /// Set up real-time expense updates
  void _setupRealTimeUpdates() {
    // Cancel existing subscription
    _expensesSubscription?.cancel();

    // Set up real-time listener for expenses
    _expensesSubscription = expenseRepository.listenToExpenses().listen(
      (expensesList) {
        log('Real-time update: received ${expensesList.length} expenses');
        expenses.assignAll(expensesList);
        _filterExpenses();
      },
      onError: (error) {
        log('Error in real-time expenses stream: $error');
        // Fallback to manual fetch on error
        fetchExpenses();
      },
    );

    // Listen for changes in expenses and update filtered list
    ever(expenses, (_) => _filterExpenses());
    ever(searchQuery, (_) => _filterExpenses());
  }

  void _onSearchChanged() {
    searchQuery.value = searchController.text;
    _filterExpenses();
  }

  // CRUD Operations

  Future<void> fetchExpenses() async {
    try {
      isLoading.value = true;
      final result = await expenseRepository.getExpenses();
      result.fold(
        (failure) => {
          log('Failed to fetch expenses: ${failure.message}'),
          SnackbarUtils.showError(
              AppStrings.errorS, 'Failed to load expenses: ${failure.message}'),
        },
        (expensesList) {
          expenses.value = expensesList;
          _filterExpenses();
        },
      );
    } catch (e) {
      log('Error loading expenses: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load expenses');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> fetchAccounts() async {
    try {
      final result = await accountRepository.getAccounts();
      result.fold(
        (failure) => {
          log('Failed to fetch accounts: ${failure.message}'),
          SnackbarUtils.showError(
              AppStrings.errorS, 'Failed to load accounts: ${failure.message}'),
        },
        (accountsList) {
          accounts.value = accountsList;
        },
      );
    } catch (e) {
      log('Error loading accounts: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load accounts');
    }
  }

  Future<void> fetchPayees() async {
    try {
      final result = await payeeRepository.getPayees();
      result.fold(
        (failure) => {
          log('Failed to fetch payees: ${failure.message}'),
          SnackbarUtils.showError(
              AppStrings.errorS, 'Failed to load payees: ${failure.message}'),
        },
        (payeesList) {
          payees.value = payeesList;
        },
      );
    } catch (e) {
      log('Error loading payees: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load payees');
    }
  }

  Future<void> fetchCategories() async {
    try {
      final result = await categoryRepository.getExpenseCategories();
      result.fold(
        (failure) => {
          log('Failed to fetch categories: ${failure.message}'),
          SnackbarUtils.showError(AppStrings.errorS,
              'Failed to load categories: ${failure.message}'),
        },
        (categoriesList) {
          // Ensure we only update the list if we got data
          if (categoriesList.isNotEmpty) {
            categories.value = categoriesList;
            log('Loaded ${categoriesList.length} expense categories');
          } else {
            log('No expense categories found in the database');
            // If no categories, retry once after a short delay (might be a network issue)
            if (categories.isEmpty) {
              Future.delayed(const Duration(seconds: 2), () {
                log('Retrying category fetch...');
                fetchCategories();
              });
            }
          }
        },
      );
    } catch (e) {
      log('Error loading categories: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to load categories');
    }
  }

  Future<void> createExpense() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    // Validate Chart of Accounts selections
    if (selectedSourceAccount.value == null) {
      SnackbarUtils.showError(
          AppStrings.errorS, 'Please select a source account');
      return;
    }

    if (selectedDestinationAccount.value == null) {
      SnackbarUtils.showError(
          AppStrings.errorS, 'Please select a destination account');
      return;
    }

    if (selectedPayee.value == null) {
      SnackbarUtils.showError(AppStrings.errorS, 'Please select a payee');
      return;
    }

    if (selectedCategory.value == null) {
      SnackbarUtils.showError(AppStrings.errorS, 'Please select a category');
      return;
    }

    isLoading.value = true;

    try {
      log('Creating new expense: ${titleController.text}');

      final amount = double.parse(amountController.text);

      final expense = ExpenseModel(
        id: '',
        title: titleController.text.trim(),
        // Legacy account fields (for backward compatibility)
        accountId: selectedSourceAccount.value!.id,
        accountName: selectedSourceAccount.value!.accountName,
        // Chart of Accounts fields
        sourceAccountId: selectedSourceAccount.value!.id,
        sourceAccountName: selectedSourceAccount.value!.accountName,
        destinationAccountId: selectedDestinationAccount.value!.id,
        destinationAccountName: selectedDestinationAccount.value!.accountName,
        amount: amount,
        createdAt: selectedDate.value,
        categoryId: selectedCategory.value!.id,
        categoryName: selectedCategory.value!.name,
        payeeId: selectedPayee.value!.id,
        payeeName: selectedPayee.value!.name,
        referenceNumber: referenceController.text.trim(),
        notes: notesController.text.trim(),
      );

      final result = await expenseRepository.createExpense(
        expense,
        file: selectedFile.value,
        fileBytes: selectedFileBytes.value != null
            ? Uint8List.fromList(selectedFileBytes.value!)
            : null,
        fileName:
            selectedFileName.value.isNotEmpty ? selectedFileName.value : null,
      );

      result.fold(
        (failure) {
          log('Failed to create expense: ${failure.message}');
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (success) {
          log('Expense created successfully');
          SnackbarUtils.showSuccess(AppStrings.success, success.message);

          // Refresh data
          fetchExpenses();
          fetchAccounts(); // Refresh to get updated balances

          // Clear form and close drawer
          clearForm();
          closeDrawer();
        },
      );
    } catch (e) {
      log('Error creating expense: $e');
      SnackbarUtils.showError(AppStrings.error, 'Failed to create expense: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteExpense(ExpenseModel expense) async {
    if (isDeleting.value) {
      return;
    }

    isDeleting.value = true;

    try {
      log('Deleting expense: ${expense.id}');

      final result = await expenseRepository.deleteExpense(
        expense.id,
        expense.accountId,
        expense.amount,
      );

      result.fold(
        (failure) {
          log('Failed to delete expense: ${failure.message}');
          SnackbarUtils.showError(AppStrings.error, failure.message);
        },
        (success) {
          log('Expense deleted successfully');

          // Remove from local list
          expenses.removeWhere((item) => item.id == expense.id);
          _filterExpenses();

          // Refresh accounts
          fetchAccounts();

          SnackbarUtils.showSuccess(AppStrings.success, success.message);

          // Force refresh to ensure pagination updates immediately
          _refreshExpenseData();
        },
      );
    } catch (e) {
      log('Error deleting expense: $e');
      SnackbarUtils.showError(AppStrings.error, 'Failed to delete expense: $e');
    } finally {
      isDeleting.value = false;
    }
  }

  // Expense Filtering

  void _filterExpenses() {
    if (searchQuery.value.isEmpty) {
      filteredExpenses.value = expenses;
    } else {
      final query = searchQuery.value.toLowerCase();
      filteredExpenses.value = expenses.where((expense) {
        return expense.accountName.toLowerCase().contains(query) ||
            expense.payeeName.toLowerCase().contains(query) ||
            expense.referenceNumber.toLowerCase().contains(query) ||
            expense.categoryName.toLowerCase().contains(query);
      }).toList();
    }
    setTotalItems(filteredExpenses.length);
  }

  void filterExpenses() {
    var filtered = List<ExpenseModel>.from(expenses);

    // Apply date filters
    switch (dateFilterType.value) {
      case 'today':
        filtered = filtered
            .where((expense) =>
                expense.createdAt.year == DateTime.now().year &&
                expense.createdAt.month == DateTime.now().month &&
                expense.createdAt.day == DateTime.now().day)
            .toList();
        break;
      case 'yesterday':
        final yesterday = DateTime.now().subtract(const Duration(days: 1));
        filtered = filtered
            .where((expense) =>
                expense.createdAt.year == yesterday.year &&
                expense.createdAt.month == yesterday.month &&
                expense.createdAt.day == yesterday.day)
            .toList();
        break;
      case 'week':
        final weekAgo = DateTime.now().subtract(const Duration(days: 7));
        filtered = filtered
            .where((expense) => expense.createdAt.isAfter(weekAgo))
            .toList();
        break;
      case 'month':
        filtered = filtered
            .where((expense) =>
                expense.createdAt.year == DateTime.now().year &&
                expense.createdAt.month == DateTime.now().month)
            .toList();
        break;
      case 'custom':
        filtered = filtered
            .where((expense) =>
                expense.createdAt.isAfter(selectedStartDate.value) &&
                expense.createdAt.isBefore(
                    selectedEndDate.value.add(const Duration(days: 1))))
            .toList();
        break;
    }

    filteredExpenses.value = filtered;
    setTotalItems(filteredExpenses.length);
  }

  List<ExpenseModel> get paginatedExpenses => paginateList(filteredExpenses);

  // Date selection

  void selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate.value,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null && picked != selectedDate.value) {
      selectedDate.value = picked;
    }
  }

  void selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedStartDate.value,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null && picked != selectedStartDate.value) {
      selectedStartDate.value = picked;
      _filterExpenses();
    }
  }

  void selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedEndDate.value,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (picked != null && picked != selectedEndDate.value) {
      selectedEndDate.value = picked;
      _filterExpenses();
    }
  }

  // UI Helpers

  void openDrawer() {
    // Refresh all form data when opening drawer
    refreshFormData();
    isDrawerOpen.value = true;
  }

  void closeDrawer() {
    isDrawerOpen.value = false;
    clearForm();
  }

  void clearForm() {
    titleController.clear();
    amountController.clear();
    referenceController.clear();
    notesController.clear();
    selectedAccount.value = null;
    selectedPayee.value = null;
    selectedCategory.value = null;
    selectedDate.value = DateTime.now();
    selectedFile.value = null;
    selectedFileBytes.value = null;
    selectedFileName.value = '';
    isWebFile.value = false;
    // Clear Chart of Accounts selections
    selectedSourceAccount.value = null;
    selectedDestinationAccount.value = null;
  }

  void toggleFilter() {
    showFilter.value = !showFilter.value;
  }

  void setDateFilterType(String type) {
    dateFilterType.value = type;
    filterExpenses();
  }

  // Formatting

  String formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy').format(date);
  }

  String formatCurrency(double amount) {
    return NumberFormat.currency(symbol: 'PKR ', decimalDigits: 2)
        .format(amount);
  }

  // Validation

  String? validateTitle(String? value) {
    if (value == null || value.isEmpty) {
      return 'Title is required';
    }
    if (value.length < 3) {
      return 'Title must be at least 3 characters';
    }
    return null;
  }

  String? validateAmount(String? value) {
    if (value == null || value.isEmpty) {
      return 'Amount is required';
    }
    if (double.tryParse(value) == null) {
      return 'Please enter a valid number';
    }
    final amount = double.parse(value);
    if (amount <= 0) {
      return 'Amount must be greater than 0';
    }

    // Check if selected account has sufficient balance
    if (selectedAccount.value != null) {
      final availableBalance = selectedAccount.value!.availableBalance;
      if (amount > availableBalance) {
        return 'Insufficient balance. Available: PKR ${availableBalance.toStringAsFixed(2)}';
      }
    }

    return null;
  }

  String? validateAccount(AccountModel? value) {
    if (value == null) {
      return 'Please select an account';
    }
    return null;
  }

  String? validatePayee(PayeeModel? value) {
    if (value == null) {
      return 'Please select a payee';
    }
    return null;
  }

  String? validateCategory(ExpenseCategoryModel? value) {
    if (value == null) {
      return 'Please select a category';
    }
    return null;
  }

  String? validateReference(String? value) {
    return null; // Optional field
  }

  void _refreshExpenseData() {
    // Immediately refresh the filtered list and pagination
    _filterExpenses();

    // Add a small delay to ensure any Firestore listeners have processed the change
    Future.delayed(const Duration(milliseconds: 500), () {
      // Force refresh the data from Firestore
      fetchExpenses();
    });
  }

  /// Refresh form data (categories, payees, accounts) when opening forms
  Future<void> refreshFormData() async {
    isLoading.value = true;
    try {
      log('Refreshing form data for expenses');

      // Refresh all form dependencies in parallel
      await Future.wait([
        fetchAccounts(),
        fetchCategories(),
        fetchPayees(),
      ]);

      log('Form data refreshed successfully');
    } catch (e) {
      log('Error refreshing form data: $e');
      SnackbarUtils.showError(AppStrings.errorS, 'Failed to refresh form data');
    } finally {
      isLoading.value = false;
    }
  }

  /// Force refresh the expenses data
  @override
  Future<void> forceRefresh() async {
    await fetchExpenses();
  }

  // Chart of Accounts methods
  void setSelectedSourceAccount(ChartOfAccountsModel? account) {
    selectedSourceAccount.value = account;
  }

  void setSelectedDestinationAccount(ChartOfAccountsModel? account) {
    selectedDestinationAccount.value = account;
  }

  String? validateSourceAccount(ChartOfAccountsModel? account) {
    if (account == null) {
      return 'Please select a source account';
    }

    // Validate account is active
    if (!account.isActive) {
      return 'Selected source account is inactive';
    }

    // Validate account category for expenses (should be Asset)
    if (account.category != AccountCategory.assets) {
      return 'Source account should be an Asset account (Cash, Bank, etc.)';
    }

    return null;
  }

  String? validateDestinationAccount(ChartOfAccountsModel? account) {
    if (account == null) {
      return 'Please select a destination account';
    }

    // Validate account is active
    if (!account.isActive) {
      return 'Selected destination account is inactive';
    }

    // Validate account category for expenses (should be Expense)
    if (account.category != AccountCategory.expenses) {
      return 'Destination account should be an Expense account (Fuel, Office Supplies, etc.)';
    }

    return null;
  }

  /// Validate the complete expense transaction
  String? validateExpenseTransaction() {
    // Validate amount
    if (amountController.text.isEmpty) {
      return 'Please enter an amount';
    }

    final amount = double.tryParse(amountController.text);
    if (amount == null || amount <= 0) {
      return 'Please enter a valid amount greater than zero';
    }

    // Validate Chart of Accounts selections
    final sourceValidation = validateSourceAccount(selectedSourceAccount.value);
    if (sourceValidation != null) {
      return sourceValidation;
    }

    final destinationValidation =
        validateDestinationAccount(selectedDestinationAccount.value);
    if (destinationValidation != null) {
      return destinationValidation;
    }

    // Validate account balance (for Asset accounts)
    if (selectedSourceAccount.value != null &&
        selectedSourceAccount.value!.category == AccountCategory.assets) {
      if (selectedSourceAccount.value!.balance < amount) {
        return 'Insufficient balance in ${selectedSourceAccount.value!.accountName}. '
            'Available: ${selectedSourceAccount.value!.balance.toStringAsFixed(2)}, '
            'Required: ${amount.toStringAsFixed(2)}';
      }
    }

    return null;
  }

  @override
  void onClose() {
    titleController.dispose();
    amountController.dispose();
    referenceController.dispose();
    notesController.dispose();
    searchController.dispose();
    _expensesSubscription?.cancel();
    super.onClose();
  }
}
