import 'dart:developer';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:logestics/core/utils/app_constants/firebase/collection_names.dart';
import 'package:logestics/firebase_service/finance/account_transaction_firebase_service.dart';
import 'package:logestics/models/finance/account_transaction_model.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:uuid/uuid.dart';

class DepositFirebaseService {
  late FirebaseFirestore _firestore;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final AccountTransactionFirebaseService _transactionService =
      AccountTransactionFirebaseService();

  DepositFirebaseService() {
    _firestore = FirebaseFirestore.instance;
  }

  // Get current user UID or return a default value if not authenticated
  String get _uid => _auth.currentUser?.uid ?? 'anonymous';

  // Future<void> createDeposit(DepositModel deposit,
  //     {File? file, Uint8List? fileBytes, String? fileName}) async {

  Future<void> createDeposit(DepositModel deposit) async {
    log('Creating deposit for account: ${deposit.accountName}');
    try {
      // First validate the account exists
      final accountRef = _firestore
          .collection(AppCollection.accountsCollection)
          .doc(deposit.accountId);

      final accountDoc = await accountRef.get();
      if (!accountDoc.exists) {
        throw Exception('Account not found');
      }

      // Get current account data
      final accountData = accountDoc.data() as Map<String, dynamic>;
      final currentBalance =
          (accountData['availableBalance'] as num).toDouble();

      // Generate a new ID for the deposit
      final depositRef =
          _firestore.collection(AppCollection.depositsCollection).doc();
      final depositId = depositRef.id;

      // Prepare deposit data
      final depositData = deposit.toJson();
      depositData['id'] = depositId;
      depositData['uid'] = _uid;

      // Create the deposit and update account balance in a batch
      final batch = _firestore.batch();

      // Set deposit document
      batch.set(depositRef, depositData);

      // Update account balance
      batch.update(
          accountRef, {'availableBalance': currentBalance + deposit.amount});

      // Commit the batch
      await batch.commit();

      // Create account transaction
      try {
        final transaction = AccountTransactionModel(
          id: const Uuid().v4(),
          uid: _uid,
          accountId: deposit.accountId,
          accountName: deposit.accountName,
          amount: deposit.amount,
          transactionDate: deposit.createdAt,
          type: TransactionType.deposit,
          description: 'Deposit from ${deposit.payerName}',
          payerId: deposit.payerId,
          payerName: deposit.payerName,
          referenceId: depositId,
          referenceName: deposit.referenceNumber,
          category: deposit.categoryName,
          metadata: {
            'depositId': depositId,
            'categoryId': deposit.categoryId,
            'categoryName': deposit.categoryName,
            'notes': deposit.notes,
          },
        );

        await _transactionService.createTransaction(transaction);
        log('Created account transaction for deposit: $depositId');
      } catch (e) {
        log('Error creating account transaction for deposit: $e');
        // Don't throw error as the main deposit was already created
      }

      log('Successfully created deposit: $depositId');
    } catch (e) {
      log('Error creating deposit: $e');
      rethrow;
    }
  }

  /// Create deposit for external company (cross-company operations)
  Future<void> createDepositForExternalCompany(
    DepositModel deposit,
    String externalCompanyUid, {
    bool skipAccountTransaction = false,
    bool skipBalanceUpdate = false,
  }) async {
    log('Creating cross-company deposit for account: ${deposit.accountName}, company: $externalCompanyUid');
    try {
      if (externalCompanyUid.isEmpty) {
        throw ArgumentError('External company UID cannot be empty');
      }

      // First validate the account exists
      final accountRef = _firestore
          .collection(AppCollection.accountsCollection)
          .doc(deposit.accountId);

      final accountDoc = await accountRef.get();
      if (!accountDoc.exists) {
        throw Exception('Account not found for external company');
      }

      // Get current account data
      final accountData = accountDoc.data() as Map<String, dynamic>;
      final currentBalance =
          (accountData['availableBalance'] as num).toDouble();

      // Generate a new ID for the deposit
      final depositRef =
          _firestore.collection(AppCollection.depositsCollection).doc();
      final depositId = depositRef.id;

      // Prepare deposit data with external company UID
      final depositData = deposit.toJson();
      depositData['id'] = depositId;
      depositData['uid'] = externalCompanyUid; // Set to external company's UID

      // Create the deposit and conditionally update account balance in a batch
      final batch = _firestore.batch();

      // Set deposit document
      batch.set(depositRef, depositData);

      // Update account balance only if not skipped
      if (!skipBalanceUpdate) {
        batch.update(
            accountRef, {'availableBalance': currentBalance + deposit.amount});
      }

      // Commit the batch
      await batch.commit();

      // Create account transaction for external company only if not skipped
      if (!skipAccountTransaction) {
        try {
          final transaction = AccountTransactionModel(
            id: const Uuid().v4(),
            uid: externalCompanyUid, // Set to external company's UID
            accountId: deposit.accountId,
            accountName: deposit.accountName,
            amount: deposit.amount,
            transactionDate: deposit.createdAt,
            type: TransactionType.deposit,
            description: 'Deposit from ${deposit.payerName}',
            payerId: deposit.payerId,
            payerName: deposit.payerName,
            referenceId: depositId,
            referenceName: deposit.referenceNumber,
            category: deposit.categoryName,
            metadata: {
              'depositId': depositId,
              'categoryId': deposit.categoryId,
              'categoryName': deposit.categoryName,
              'notes': deposit.notes,
            },
          );

          final transactionService = AccountTransactionFirebaseService();
          await transactionService.createTransactionForExternalCompany(
              transaction, externalCompanyUid);
          log('Created cross-company account transaction for deposit: $depositId');
        } catch (e) {
          log('Error creating cross-company account transaction for deposit: $e');
          // Don't throw error as the main deposit was already created
        }
      } else {
        log('Skipped account transaction creation for deposit: $depositId (handled separately)');
      }

      log('Successfully created cross-company deposit: $depositId for company: $externalCompanyUid');
    } catch (e) {
      log('Error creating cross-company deposit: $e');
      rethrow;
    }
  }

  Future<List<DepositModel>> getDeposits() async {
    log('Fetching deposits from Firestore for user: $_uid');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.depositsCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .get();

      log('Raw snapshot from Firestore: ${snapshot.docs.length} documents');

      final deposits = <DepositModel>[];
      for (final doc in snapshot.docs) {
        try {
          final data = doc.data();
          log('Processing document ${doc.id}: $data');
          final deposit = DepositModel.fromJson(data);
          deposits.add(deposit);
        } catch (e) {
          log('Error processing deposit document ${doc.id}: $e');
          // Skip this document and continue with others
        }
      }

      // Sort deposits by createdAt in descending order (newest first)
      deposits.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      log('Successfully processed ${deposits.length} deposits out of ${snapshot.docs.length} documents');
      return deposits;
    } catch (e) {
      log('Error fetching deposits: $e');
      rethrow;
    }
  }

  Future<void> updateDeposit(DepositModel deposit) async {
    log('Updating deposit: ${deposit.id}');
    try {
      if (deposit.id.isEmpty) {
        throw ArgumentError('Deposit ID cannot be empty');
      }

      // First check if the deposit belongs to the current user
      final depositDoc = await _firestore
          .collection(AppCollection.depositsCollection)
          .doc(deposit.id)
          .get();

      if (!depositDoc.exists) {
        throw Exception('Deposit not found');
      }

      final depositData = depositDoc.data() as Map<String, dynamic>;
      if (depositData['uid'] != _uid) {
        throw Exception('You do not have permission to update this deposit');
      }

      final depositRef = _firestore
          .collection(AppCollection.depositsCollection)
          .doc(deposit.id);
      final updatedDepositData = deposit.toJson();

      // Preserve the original UID
      updatedDepositData['uid'] = depositData['uid'];

      await depositRef.update(updatedDepositData);
      log('Successfully updated deposit: ${deposit.id}');
    } catch (e) {
      log('Error updating deposit: $e');
      rethrow;
    }
  }

  Future<void> deleteDeposit(String depositId) async {
    log('Deleting deposit: $depositId');
    try {
      if (depositId.isEmpty) {
        throw ArgumentError('Deposit ID cannot be empty');
      }

      // First check if the deposit belongs to the current user
      final depositDoc = await _firestore
          .collection(AppCollection.depositsCollection)
          .doc(depositId)
          .get();

      if (!depositDoc.exists) {
        throw Exception('Deposit not found');
      }

      final depositData = depositDoc.data() as Map<String, dynamic>;
      if (depositData['uid'] != _uid) {
        throw Exception('You do not have permission to delete this deposit');
      }

      // Get the account reference and deposit amount
      final accountId = depositData['accountId'] as String;
      final depositAmount = (depositData['amount'] as num).toDouble();

      final accountRef = _firestore
          .collection(AppCollection.accountsCollection)
          .doc(accountId);

      final accountDoc = await accountRef.get();
      if (!accountDoc.exists) {
        throw Exception('Associated account not found');
      }

      // Get current account balance
      final accountData = accountDoc.data() as Map<String, dynamic>;
      final currentBalance =
          (accountData['availableBalance'] as num).toDouble();

      // Calculate new balance after deposit deletion
      final newBalance = currentBalance - depositAmount;

      // Log detailed balance information
      log('💰 Account balance before deletion: $currentBalance');
      log('💸 Deposit amount to subtract: $depositAmount');
      log('🧮 Calculated new balance: $newBalance');

      // Check for potential negative balance
      if (newBalance < 0) {
        log('⚠️ WARNING: Deposit deletion would result in negative balance!');
        log('   Account: ${accountData['name'] ?? 'Unknown'}');
        log('   Current Balance: $currentBalance');
        log('   Deposit Amount: $depositAmount');
        log('   Resulting Balance: $newBalance');
        log('   This might indicate a double-deletion or incorrect balance state');
      }

      // Create a batch operation
      final batch = _firestore.batch();

      // Delete the deposit document
      batch.delete(_firestore
          .collection(AppCollection.depositsCollection)
          .doc(depositId));

      // Update account balance by subtracting the deposit amount
      batch.update(accountRef, {'availableBalance': newBalance});

      // Commit the batch
      await batch.commit();

      // Delete associated account transactions
      try {
        final transactionsSnapshot = await _firestore
            .collection(AppCollection.transactionsCollection)
            .where('referenceId', isEqualTo: depositId)
            .get();

        for (final doc in transactionsSnapshot.docs) {
          await doc.reference.delete();
        }
        log('Deleted associated account transactions for deposit: $depositId');
      } catch (e) {
        log('Error deleting account transactions for deposit: $e');
        // Don't throw error as the main deposit was already deleted
      }

      log('Successfully deleted deposit: $depositId');
    } catch (e) {
      log('Error deleting deposit: $e');
      rethrow;
    }
  }

  /// Stream to listen for real-time updates to deposits
  Stream<List<DepositModel>> listenToDeposits() {
    try {
      return _firestore
          .collection(AppCollection.depositsCollection)
          .where('uid', isEqualTo: _uid) // Filter by current user's UID
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs
              .map((doc) => DepositModel.fromJson(doc.data()))
              .toList());
    } catch (e) {
      log('Error listening to deposits: $e', error: e);
      return Stream.value([]);
    }
  }
}
