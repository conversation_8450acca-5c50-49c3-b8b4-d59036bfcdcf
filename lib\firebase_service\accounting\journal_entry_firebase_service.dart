import 'dart:developer';
import 'dart:math' as math;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../core/utils/app_constants/firebase/collection_names.dart';
import '../../core/services/accounting_validation_service.dart';
import '../../core/services/account_type_helper_service.dart';
import '../../core/services/balance_recalculation_service.dart';
import '../../models/finance/journal_entry_model.dart';
import '../../models/finance/chart_of_accounts_model.dart';

class JournalEntryFirebaseService {
  late FirebaseFirestore _firestore;
  late BalanceRecalculationService _balanceRecalculationService;

  // Get current user's UID or return 'anonymous' if not authenticated
  String get _uid => FirebaseAuth.instance.currentUser?.uid ?? 'anonymous';

  JournalEntryFirebaseService() {
    _firestore = FirebaseFirestore.instance;
    _balanceRecalculationService = BalanceRecalculationService();
  }

  /// Validate double-entry rules before creating/updating
  ValidationResult validateDoubleEntry(JournalEntryModel journalEntry) {
    log('Validating journal entry: ${journalEntry.description}');

    // Use comprehensive validation service
    final validationResult =
        AccountingValidationService.validateJournalEntry(journalEntry);

    // Log validation results
    if (!validationResult.isValid) {
      log('Journal entry validation failed:');
      for (final error in validationResult.errors) {
        log('  ERROR: $error');
      }
    }

    if (validationResult.hasWarnings) {
      log('Journal entry validation warnings:');
      for (final warning in validationResult.warnings) {
        log('  WARNING: $warning');
      }
    }

    return validationResult;
  }

  /// Legacy method for backward compatibility
  bool validateDoubleEntryLegacy(JournalEntryModel journalEntry) {
    final result = validateDoubleEntry(journalEntry);
    return result.isValid;
  }

  /// Create a new journal entry with lines
  Future<void> createJournalEntry(JournalEntryModel journalEntry) async {
    log('🔍 [JOURNAL CREATE] Creating journal entry: ${journalEntry.description}');
    log('🔍 [JOURNAL CREATE] Entry details - Ref: ${journalEntry.referenceNumber}, Source: ${journalEntry.sourceTransactionId}, Type: ${journalEntry.sourceTransactionType}');
    log('🔍 [JOURNAL CREATE] Lines count: ${journalEntry.lines.length}');
    try {
      // Note: Double-entry validation removed to allow unbalanced voucher saves
      log('🔍 [JOURNAL CREATE] Creating journal entry without balance validation: ${journalEntry.description}');

      // Validate basic requirements
      if (journalEntry.lines.isEmpty) {
        throw Exception('Journal entry must have at least one line');
      }

      if (journalEntry.description.trim().isEmpty) {
        throw Exception('Journal entry must have a description');
      }

      // Check for duplicate entries based on source transaction
      if (journalEntry.sourceTransactionId != null &&
          journalEntry.sourceTransactionType != null) {
        final existingEntries = await _checkForDuplicateEntries(
          journalEntry.sourceTransactionId!,
          journalEntry.sourceTransactionType!,
        );
        if (existingEntries.isNotEmpty) {
          log('❌ [JOURNAL CREATE] Duplicate journal entry detected for ${journalEntry.sourceTransactionType}: ${journalEntry.sourceTransactionId}');
          log('❌ [JOURNAL CREATE] Found ${existingEntries.length} existing entries');
          for (final doc in existingEntries) {
            final data = doc.data() as Map<String, dynamic>;
            log('❌ [JOURNAL CREATE] Existing entry: ${doc.id}, Status: ${data['status']}, Created: ${data['createdAt']}');
          }
          throw Exception('Journal entry already exists for this transaction');
        } else {
          log('✅ [JOURNAL CREATE] No duplicate entries found for ${journalEntry.sourceTransactionType}: ${journalEntry.sourceTransactionId}');
        }
      }

      // Prepare variables outside transaction scope
      final entryId = journalEntry.id.isNotEmpty
          ? journalEntry.id
          : 'JE_${DateTime.now().millisecondsSinceEpoch}_${math.Random().nextInt(9999)}';

      // Generate entry number if not provided
      String entryNumber = journalEntry.entryNumber;
      if (entryNumber.isEmpty) {
        entryNumber = await getNextJournalEntryNumber();
      }

      // Use Firebase transaction for atomicity (consistent with voucher atomic transactions)
      await _firestore.runTransaction((transaction) async {
        final entryRef = _firestore
            .collection(AppCollection.journalEntriesCollection)
            .doc(entryId);

        // Check if entry already exists
        final existingDoc = await transaction.get(entryRef);
        if (existingDoc.exists) {
          throw Exception('Journal entry with ID $entryId already exists');
        }

        // Create journal entry with embedded lines (new atomic transaction format)
        final entryData = journalEntry.toFirestore();
        entryData['id'] = entryId; // Keep the same ID
        entryData['entryNumber'] = entryNumber;
        entryData['uid'] = _uid;

        log('🔍 [JOURNAL CREATE] Using document ID: $entryId (matches journal entry ID)');
        log('🔍 [JOURNAL CREATE] Storing ${journalEntry.lines.length} lines embedded in journal entry document');

        // Store journal entry with embedded lines (consistent with atomic voucher transactions)
        transaction.set(entryRef, entryData);

        // Also create lines in separate collection for backward compatibility
        // This ensures existing code that reads from journal_entry_lines still works
        for (final line in journalEntry.lines) {
          final lineRef = _firestore
              .collection(AppCollection.journalEntryLinesCollection)
              .doc();
          final lineId = lineRef.id;

          final lineData = line.toFirestore();
          lineData['id'] = lineId;
          lineData['journalEntryId'] = entryId;

          transaction.set(lineRef, lineData);
        }

        log('✅ [JOURNAL CREATE] Journal entry prepared for atomic commit with embedded and separate lines');
      });
      log('✅ [JOURNAL CREATE] Successfully created journal entry: $entryId (Entry Number: $entryNumber)');
      log('✅ [JOURNAL CREATE] Journal entry created for voucher reference: ${journalEntry.referenceNumber}');
    } catch (e) {
      log('❌ [JOURNAL CREATE] Error creating journal entry: $e');
      log('❌ [JOURNAL CREATE] Failed entry details - Ref: ${journalEntry.referenceNumber}, Source: ${journalEntry.sourceTransactionId}');
      rethrow;
    }
  }

  /// Get all journal entries for the current user
  Future<List<JournalEntryModel>> getJournalEntries() async {
    log('🔍 [DEBUG] Starting to fetch journal entries from Firestore');
    log('🔍 [DEBUG] Current UID: $_uid');
    log('🔍 [DEBUG] Collection: ${AppCollection.journalEntriesCollection}');

    // First, let's check if there are ANY documents in the collection
    try {
      final allDocsSnapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .limit(5)
          .get();
      log('🔍 [DEBUG] Total documents in collection (first 5): ${allDocsSnapshot.docs.length}');

      for (final doc in allDocsSnapshot.docs) {
        final data = doc.data();
        log('🔍 [DEBUG] Document ${doc.id}: uid=${data['uid']}, entryNumber=${data['entryNumber']}');
      }
    } catch (e) {
      log('❌ [DEBUG] Error checking all documents: $e');
    }

    try {
      // Use simple query without orderBy to avoid index issues, then sort in memory
      final snapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .get();

      log('🔍 [DEBUG] Firestore query executed successfully');
      log('🔍 [DEBUG] Raw documents found: ${snapshot.docs.length}');

      final entries = <JournalEntryModel>[];

      // Log the order documents are retrieved from Firestore (before sorting)
      log('🔍 [DEBUG] Documents retrieved from Firestore (unsorted):');
      for (int i = 0; i < snapshot.docs.length && i < 5; i++) {
        final doc = snapshot.docs[i];
        final entryData = doc.data();
        final createdAtTimestamp = entryData['createdAt'] as Timestamp?;
        final createdAt = createdAtTimestamp?.toDate();
        final entryNumber = entryData['entryNumber'];
        final description = entryData['description'];
        final entryType = entryData['entryType'];
        final sourceType = entryData['sourceTransactionType'];
        log('   Doc $i: $entryNumber - $description - Created: $createdAt - Type: $entryType - Source: $sourceType');
      }

      for (final doc in snapshot.docs) {
        log('🔍 [DEBUG] Processing document: ${doc.id}');

        try {
          final docData = doc.data();

          // Check if lines are embedded in the document (new atomic transaction format)
          final embeddedLines = docData['lines'] as List<dynamic>?;

          if (embeddedLines != null && embeddedLines.isNotEmpty) {
            log('🔍 [DEBUG] Found ${embeddedLines.length} embedded lines for entry ${doc.id}');

            // Create journal entry using fromFirestore which handles embedded lines automatically
            final entry = JournalEntryModel.fromFirestore(doc);

            entries.add(entry);
            log('🔍 [DEBUG] Successfully added entry with embedded lines: ${entry.entryNumber} (${entry.lines.length} lines)');
          } else {
            log('🔍 [DEBUG] No embedded lines found, checking separate collection for entry ${doc.id}');

            // Fallback: Get lines from separate collection (legacy format)
            final linesSnapshot = await _firestore
                .collection(AppCollection.journalEntryLinesCollection)
                .where('journalEntryId', isEqualTo: doc.id)
                .orderBy('createdAt')
                .get();

            log('🔍 [DEBUG] Lines found in separate collection for entry ${doc.id}: ${linesSnapshot.docs.length}');

            final lines = linesSnapshot.docs
                .map((lineDoc) =>
                    JournalEntryLineModel.fromFirestore(lineDoc.data()))
                .toList();

            // Create journal entry with proper Timestamp handling
            final entry = _createJournalEntryFromFirestore(doc, lines);

            entries.add(entry);
            log('🔍 [DEBUG] Successfully added entry with separate lines: ${entry.entryNumber}');
          }
        } catch (lineError) {
          log('⚠️ [DEBUG] Error loading lines for entry ${doc.id}, using fallback: $lineError');

          // Final fallback: Create entry without lines if both methods fail
          final entry = _createJournalEntryFromFirestore(doc, []);

          entries.add(entry);
          log('🔍 [DEBUG] Added entry with fallback (no lines): ${entry.entryNumber}');
        }
      }

      // Sort entries for proper entry-wise chronological ordering (newest entries first)
      // Primary sort: by creation time (descending - newest entries first)
      // Secondary sort: by entry number (descending - highest number first for same creation time)
      entries.sort((a, b) {
        final creationComparison = b.createdAt.compareTo(a.createdAt);
        if (creationComparison != 0) {
          return creationComparison;
        }
        // If creation times are the same, sort by entry number (descending)
        return b.entryNumber.compareTo(a.entryNumber);
      });

      log('✅ [DEBUG] Successfully fetched and sorted ${entries.length} journal entries');
      log('🔍 [DEBUG] First few entries after sorting (newest first):');
      for (int i = 0; i < entries.length && i < 5; i++) {
        final entry = entries[i];
        log('   ${i + 1}. ${entry.entryNumber} - ${entry.description} - Created: ${entry.createdAt} - Type: ${entry.entryType.name} - Source: ${entry.sourceTransactionType ?? 'manual'}');
      }

      return entries;
    } catch (e) {
      log('❌ [DEBUG] Error fetching journal entries: $e');
      rethrow;
    }
  }

  /// Get journal entries by date range
  Future<List<JournalEntryModel>> getJournalEntriesByDateRange(
      DateTime startDate, DateTime endDate) async {
    log('Fetching journal entries by date range: $startDate to $endDate');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .where('entryDate',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('entryDate', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('createdAt', descending: true)
          .get();

      final entries = <JournalEntryModel>[];

      for (final doc in snapshot.docs) {
        try {
          final docData = doc.data();

          // Check if lines are embedded in the document (new atomic transaction format)
          final embeddedLines = docData['lines'] as List<dynamic>?;

          if (embeddedLines != null && embeddedLines.isNotEmpty) {
            // Create journal entry using fromFirestore which handles embedded lines automatically
            final entry = JournalEntryModel.fromFirestore(doc);
            entries.add(entry);
          } else {
            // Fallback: Get lines from separate collection (legacy format)
            final linesSnapshot = await _firestore
                .collection(AppCollection.journalEntryLinesCollection)
                .where('journalEntryId', isEqualTo: doc.id)
                .orderBy('createdAt')
                .get();

            final lines = linesSnapshot.docs
                .map((lineDoc) =>
                    JournalEntryLineModel.fromFirestore(lineDoc.data()))
                .toList();

            // Create journal entry with proper Timestamp handling
            final entry = _createJournalEntryFromFirestore(doc, lines);
            entries.add(entry);
          }
        } catch (e) {
          log('⚠️ Error loading journal entry ${doc.id}: $e');
          // Continue processing other entries
        }
      }

      // Sort entries by creation time (descending) for consistent newest-first ordering
      entries.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // Debug: Log the first few entries to verify sorting
      log('🔍 Date range query sorted ${entries.length} journal entries by creation time (newest first):');
      for (int i = 0; i < entries.length && i < 3; i++) {
        final entry = entries[i];
        log('   ${i + 1}. ${entry.entryNumber} - ${entry.description} - Created: ${entry.createdAt} - Type: ${entry.entryType.name} - Source: ${entry.sourceTransactionType ?? 'manual'}');
      }

      log('Successfully fetched ${entries.length} journal entries for date range');
      return entries;
    } catch (e) {
      log('Error fetching journal entries by date range: $e');

      // If compound query fails due to missing index, try fallback query
      if (e.toString().contains('index') ||
          e.toString().contains('FAILED_PRECONDITION')) {
        log('🔄 Attempting fallback date range query without compound index...');
        return await _getFallbackJournalEntriesByDateRange(startDate, endDate);
      }

      rethrow;
    }
  }

  /// Fallback method for date range queries without compound indexes
  Future<List<JournalEntryModel>> _getFallbackJournalEntriesByDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      log('🔄 Using fallback query for journal entries by date range');

      // Simple query with just uid filter
      final snapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .get();

      var entries = <JournalEntryModel>[];

      for (final doc in snapshot.docs) {
        try {
          final entryData = doc.data();
          final entryDate = _parseDateTime(entryData['entryDate']);

          // Filter by date range in memory
          if (entryDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
              entryDate.isBefore(endDate.add(const Duration(days: 1)))) {
            // Simple lines query
            final linesSnapshot = await _firestore
                .collection(AppCollection.journalEntryLinesCollection)
                .where('journalEntryId', isEqualTo: doc.id)
                .get();

            final lines = linesSnapshot.docs
                .map((lineDoc) =>
                    JournalEntryLineModel.fromFirestore(lineDoc.data()))
                .toList();

            lines.sort((a, b) => a.createdAt.compareTo(b.createdAt));

            // Create journal entry with proper Timestamp handling
            final entry = _createJournalEntryFromFirestore(doc, lines);

            entries.add(entry);
          }
        } catch (e) {
          log('⚠️ Error parsing journal entry document ${doc.id}: $e');
        }
      }

      // Sort entries in memory by creation time (descending) for consistent newest-first ordering
      entries.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      log('✅ Fallback date range query successful: ${entries.length} entries');
      return entries;
    } catch (e) {
      log('❌ Fallback date range query also failed: $e');
      return [];
    }
  }

  /// Get journal entry by ID
  Future<JournalEntryModel?> getJournalEntryById(String entryId) async {
    log('Fetching journal entry by ID: $entryId');
    try {
      final doc = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId)
          .get();

      if (!doc.exists) {
        log('Journal entry not found: $entryId');
        return null;
      }

      final entryData = doc.data() as Map<String, dynamic>;

      // Verify the entry belongs to the current user
      if (entryData['uid'] != _uid) {
        log('Journal entry does not belong to current user: $entryId');
        return null;
      }

      // Check if lines are embedded in the document (new atomic transaction format)
      final embeddedLines = entryData['lines'] as List<dynamic>?;

      JournalEntryModel entry;
      if (embeddedLines != null && embeddedLines.isNotEmpty) {
        // Create journal entry using fromFirestore which handles embedded lines automatically
        entry = JournalEntryModel.fromFirestore(doc);
      } else {
        // Fallback: Get lines from separate collection (legacy format)
        final linesSnapshot = await _firestore
            .collection(AppCollection.journalEntryLinesCollection)
            .where('journalEntryId', isEqualTo: entryId)
            .orderBy('createdAt')
            .get();

        final lines = linesSnapshot.docs
            .map((lineDoc) =>
                JournalEntryLineModel.fromFirestore(lineDoc.data()))
            .toList();

        // Create journal entry with proper Timestamp handling
        entry = _createJournalEntryFromFirestore(doc, lines);
      }

      log('Successfully fetched journal entry: ${entry.description}');
      return entry;
    } catch (e) {
      log('Error fetching journal entry by ID: $e');
      rethrow;
    }
  }

  /// Get journal entry by ID for cross-company operations (bypasses UID validation)
  /// Used specifically for voucher payment loans where journal entries may be accessed across companies
  Future<JournalEntryModel?> getJournalEntryByIdCrossCompany(
      String entryId) async {
    log('Fetching journal entry by ID (cross-company): $entryId');
    try {
      final doc = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId)
          .get();

      if (!doc.exists) {
        log('Journal entry not found (cross-company): $entryId');
        return null;
      }

      final entryData = doc.data() as Map<String, dynamic>;

      // Log the UID for debugging but don't validate ownership for cross-company access
      log('Journal entry found (cross-company): $entryId, owner UID: ${entryData['uid']}, current UID: $_uid');

      // Check if lines are embedded in the document (new atomic transaction format)
      final embeddedLines = entryData['lines'] as List<dynamic>?;

      JournalEntryModel entry;
      if (embeddedLines != null && embeddedLines.isNotEmpty) {
        // Create journal entry using fromFirestore which handles embedded lines automatically
        entry = JournalEntryModel.fromFirestore(doc);
      } else {
        // Fallback: Get lines from separate collection (legacy format)
        final linesSnapshot = await _firestore
            .collection(AppCollection.journalEntryLinesCollection)
            .where('journalEntryId', isEqualTo: entryId)
            .orderBy('createdAt')
            .get();

        final lines = linesSnapshot.docs
            .map((lineDoc) =>
                JournalEntryLineModel.fromFirestore(lineDoc.data()))
            .toList();

        // Create journal entry with proper Timestamp handling
        entry = _createJournalEntryFromFirestore(doc, lines);
      }

      log('Successfully fetched journal entry (cross-company): ${entry.entryNumber}');
      return entry;
    } catch (e) {
      log('Error fetching journal entry by ID (cross-company): $e');
      rethrow;
    }
  }

  /// Update journal entry status
  Future<void> updateJournalEntryStatus(
      String entryId, JournalEntryStatus status) async {
    log('Updating journal entry status: $entryId to ${status.displayName}');
    try {
      await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId)
          .update({
        'status': status.name,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      log('Successfully updated journal entry status: $entryId');
    } catch (e) {
      log('Error updating journal entry status: $e');
      rethrow;
    }
  }

  /// Post a journal entry (change status from draft to posted) with atomic account balance updates
  Future<void> postJournalEntry(String entryId) async {
    log('Posting journal entry with atomic account balance updates: $entryId');

    try {
      // First, get the journal entry to access its lines
      final entryDoc = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId)
          .get();

      if (!entryDoc.exists) {
        throw Exception('Journal entry not found: $entryId');
      }

      final journalEntry = JournalEntryModel.fromFirestore(entryDoc);

      // Note: Double-entry validation removed to allow unbalanced journal entries
      log('Posting journal entry without balance validation: ${journalEntry.description}');

      // Use Firebase transaction for atomic operations
      await _firestore.runTransaction((transaction) async {
        log('Starting atomic transaction for journal entry posting: $entryId');

        // 1. Update journal entry status to posted
        final entryRef = _firestore
            .collection(AppCollection.journalEntriesCollection)
            .doc(entryId);

        transaction.update(entryRef, {
          'status': JournalEntryStatus.posted.name,
          'updatedAt': Timestamp.fromDate(DateTime.now()),
        });

        // 2. Update account balances for all affected accounts atomically
        final accountUpdates = <String, double>{};

        // Collect all account balance changes
        for (final line in journalEntry.lines) {
          final accountId = line.accountId;

          // Get current account data
          final accountQuery = await _firestore
              .collection(AppCollection.chartOfAccountsCollection)
              .where('uid', isEqualTo: _uid)
              .where('id', isEqualTo: accountId)
              .limit(1)
              .get();

          if (accountQuery.docs.isEmpty) {
            throw Exception('Account not found: $accountId');
          }

          final accountDoc = accountQuery.docs.first;
          final accountData = accountDoc.data();
          final account = ChartOfAccountsModel.fromJson(accountData);

          // Calculate balance change using AccountTypeHelperService
          final balanceChange = AccountTypeHelperService.calculateBalanceChange(
            accountType: account.accountType,
            debitAmount: line.debitAmount,
            creditAmount: line.creditAmount,
          );

          // Accumulate balance changes for this account
          accountUpdates[accountId] =
              (accountUpdates[accountId] ?? account.balance) + balanceChange;

          log('Account ${account.accountName}: Current=${account.balance}, Change=$balanceChange, New=${accountUpdates[accountId]}');
        }

        // Apply all account balance updates atomically (including parent accounts)
        for (final entry in accountUpdates.entries) {
          final accountId = entry.key;
          final newBalance = entry.value;

          final accountQuery = await _firestore
              .collection(AppCollection.chartOfAccountsCollection)
              .where('uid', isEqualTo: _uid)
              .where('id', isEqualTo: accountId)
              .limit(1)
              .get();

          if (accountQuery.docs.isNotEmpty) {
            final accountRef = accountQuery.docs.first.reference;
            final accountData = accountQuery.docs.first.data();
            final account = ChartOfAccountsModel.fromJson(accountData);

            // Update the account balance
            transaction.update(accountRef, {'balance': newBalance});
            log('Updated account balance atomically: $accountId = $newBalance');

            // Update parent account balance if this is a child account
            await _updateParentAccountBalance(
              transaction,
              account,
              newBalance - account.balance, // balance change amount
            );
          }
        }

        log('All account balances updated atomically for journal entry: $entryId');
      });

      // After successful posting, trigger balance recalculation for backdated entries
      await _balanceRecalculationService.onJournalEntryPosted(journalEntry);

      log('Successfully posted journal entry and updated account balances atomically: $entryId');
    } catch (e) {
      log('Error posting journal entry: $e');
      rethrow;
    }
  }

  /// Update parent account balance when child account is affected
  Future<void> _updateParentAccountBalance(
    Transaction transaction,
    ChartOfAccountsModel childAccount,
    double balanceChange,
  ) async {
    try {
      // Check if this account has a parent
      if (childAccount.parentAccountId == null ||
          childAccount.parentAccountId!.isEmpty) {
        return; // No parent account to update
      }

      log('Updating parent account balance for child: ${childAccount.accountName}');

      // Get parent account
      final parentQuery = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('uid', isEqualTo: _uid)
          .where('id', isEqualTo: childAccount.parentAccountId)
          .limit(1)
          .get();

      if (parentQuery.docs.isEmpty) {
        log('Parent account not found: ${childAccount.parentAccountId}');
        return;
      }

      final parentDoc = parentQuery.docs.first;
      final parentData = parentDoc.data();
      final parentAccount = ChartOfAccountsModel.fromJson(parentData);

      // Calculate new parent balance
      final newParentBalance = parentAccount.balance + balanceChange;

      // Update parent account balance
      transaction.update(parentDoc.reference, {'balance': newParentBalance});
      log('Updated parent account ${parentAccount.accountName}: ${parentAccount.balance} -> $newParentBalance');

      // Recursively update grandparent if exists
      await _updateParentAccountBalance(
        transaction,
        parentAccount,
        balanceChange,
      );
    } catch (e) {
      log('Error updating parent account balance: $e');
      // Don't throw, just log the error to avoid breaking the main transaction
    }
  }

  /// Reverse a journal entry
  Future<void> reverseJournalEntry(
      String entryId, JournalEntryModel reversalEntry) async {
    log('Reversing journal entry: $entryId');
    try {
      final batch = _firestore.batch();

      // Update original entry status to reversed
      final originalEntryRef = _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId);

      batch.update(originalEntryRef, {
        'status': JournalEntryStatus.reversed.name,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });

      // Create reversal entry
      final reversalEntryRef =
          _firestore.collection(AppCollection.journalEntriesCollection).doc();
      final reversalEntryId = reversalEntryRef.id;

      final reversalData = reversalEntry.toFirestore();
      reversalData['id'] = reversalEntryId;
      reversalData['uid'] = _uid;
      reversalData['reversalOfEntryId'] = entryId;

      batch.set(reversalEntryRef, reversalData);

      // Create reversal entry lines
      for (final line in reversalEntry.lines) {
        final lineRef = _firestore
            .collection(AppCollection.journalEntryLinesCollection)
            .doc();
        final lineId = lineRef.id;

        final lineData = line.toFirestore();
        lineData['id'] = lineId;
        lineData['journalEntryId'] = reversalEntryId;

        batch.set(lineRef, lineData);
      }

      await batch.commit();
      log('Successfully reversed journal entry: $entryId');
    } catch (e) {
      log('Error reversing journal entry: $e');
      rethrow;
    }
  }

  /// Delete a journal entry (only drafts can be deleted)
  Future<void> deleteJournalEntry(String entryId) async {
    log('Deleting journal entry: $entryId');
    try {
      final batch = _firestore.batch();

      // Delete journal entry lines first
      final linesSnapshot = await _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('journalEntryId', isEqualTo: entryId)
          .get();

      for (final lineDoc in linesSnapshot.docs) {
        batch.delete(lineDoc.reference);
      }

      // Delete journal entry
      final entryRef = _firestore
          .collection(AppCollection.journalEntriesCollection)
          .doc(entryId);

      batch.delete(entryRef);

      await batch.commit();
      log('Successfully deleted journal entry: $entryId');
    } catch (e) {
      log('Error deleting journal entry: $e');
      rethrow;
    }
  }

  /// Check for duplicate journal entries based on source transaction
  Future<List<QueryDocumentSnapshot>> _checkForDuplicateEntries(
    String sourceTransactionId,
    String sourceTransactionType,
  ) async {
    try {
      log('🔍 [DUPLICATE CHECK] Checking for existing journal entries');
      log('🔍 [DUPLICATE CHECK] Source Transaction ID: $sourceTransactionId');
      log('🔍 [DUPLICATE CHECK] Source Transaction Type: $sourceTransactionType');
      log('🔍 [DUPLICATE CHECK] User ID: $_uid');

      final query = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .where('sourceTransactionId', isEqualTo: sourceTransactionId)
          .where('sourceTransactionType', isEqualTo: sourceTransactionType)
          .get();

      log('🔍 [DUPLICATE CHECK] Found ${query.docs.length} existing entries');

      // Filter out any entries that might be in draft or failed status
      final validEntries = query.docs.where((doc) {
        final data = doc.data();
        final status = data['status'] as String?;

        // Only consider posted or pending entries as duplicates
        // Draft or failed entries should not prevent new creation
        final isValidDuplicate = status == 'posted' || status == 'pending';

        if (!isValidDuplicate) {
          log('🔍 [DUPLICATE CHECK] Ignoring entry ${doc.id} with status: $status');
        }

        return isValidDuplicate;
      }).toList();

      log('🔍 [DUPLICATE CHECK] Valid duplicate entries: ${validEntries.length}');
      return validEntries;
    } catch (e) {
      log('❌ [DUPLICATE CHECK] Error checking for duplicate entries: $e');
      return [];
    }
  }

  /// Get journal entries by source transaction
  Future<List<JournalEntryModel>> getJournalEntriesBySource(
    String sourceTransactionId,
    String sourceTransactionType,
  ) async {
    try {
      final query = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .where('sourceTransactionId', isEqualTo: sourceTransactionId)
          .where('sourceTransactionType', isEqualTo: sourceTransactionType)
          .get();

      final entries = <JournalEntryModel>[];
      for (final doc in query.docs) {
        // Create entry from Firestore document
        final entry = JournalEntryModel.fromFirestore(doc);
        entries.add(entry);
      }

      return entries;
    } catch (e) {
      log('Error getting journal entries by source: $e');
      return [];
    }
  }

  /// Get next journal entry number
  Future<String> getNextJournalEntryNumber() async {
    log('Getting next journal entry number');
    try {
      // Get the highest existing journal entry number
      final snapshot = await _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('entryNumber', descending: true)
          .limit(1)
          .get();

      int nextNumber = 1;

      if (snapshot.docs.isNotEmpty) {
        final lastEntry = snapshot.docs.first.data();
        final lastEntryNumber =
            lastEntry['entryNumber'] as String? ?? 'JE000000';

        // Extract number from format like "JE000001"
        final numberPart = lastEntryNumber.replaceAll(RegExp(r'[^0-9]'), '');
        final lastNumber = int.tryParse(numberPart) ?? 0;
        nextNumber = lastNumber + 1;
      }

      final entryNumber = 'JE${nextNumber.toString().padLeft(6, '0')}';
      log('Next journal entry number: $entryNumber');
      return entryNumber;
    } catch (e) {
      log('Error getting next journal entry number: $e');
      // Fallback to timestamp-based number
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fallbackNumber = 'JE${timestamp.toString().substring(7)}';
      log('Using fallback journal entry number: $fallbackNumber');
      return fallbackNumber;
    }
  }

  /// Get journal entries by account
  Future<List<JournalEntryLineModel>> getJournalEntriesByAccount(
      String accountId) async {
    log('Fetching journal entries by account: $accountId');
    try {
      final snapshot = await _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('accountId', isEqualTo: accountId)
          .orderBy('createdAt', descending: true)
          .get();

      final lines = snapshot.docs
          .map((doc) => JournalEntryLineModel.fromFirestore(doc.data()))
          .toList();

      log('Successfully fetched ${lines.length} journal entry lines for account');
      return lines;
    } catch (e) {
      log('Error fetching journal entries by account: $e');
      rethrow;
    }
  }

  /// Get journal entries for account with pagination and filtering
  Future<List<JournalEntryModel>> getJournalEntriesForAccount({
    required String accountId,
    required String uid,
    int limit = 25,
    QueryDocumentSnapshot? lastDocument,
    DateTime? startDate,
    DateTime? endDate,
    List<JournalEntryStatus>? statusFilter,
  }) async {
    log('Fetching journal entries for account: $accountId with pagination');
    try {
      // First, get journal entry IDs that contain lines for this account
      Query lineQuery = _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('accountId', isEqualTo: accountId);

      final lineSnapshot = await lineQuery.get();
      final journalEntryIds = lineSnapshot.docs
          .map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return data['journalEntryId'] as String;
          })
          .toSet()
          .toList();

      if (journalEntryIds.isEmpty) {
        log('No journal entries found for account: $accountId');
        return [];
      }

      log('🔍 Found ${journalEntryIds.length} journal entry IDs for account: $accountId');

      // Firebase whereIn has a limit of 10 items, so we need to batch the queries
      final allJournalEntries = <JournalEntryModel>[];

      // Process journal entry IDs in batches of 10
      for (int i = 0; i < journalEntryIds.length; i += 10) {
        final batchIds = journalEntryIds.skip(i).take(10).toList();
        log('🔍 Processing batch ${(i ~/ 10) + 1}: ${batchIds.length} IDs');

        // Now get the actual journal entries for this batch
        Query entryQuery = _firestore
            .collection(AppCollection.journalEntriesCollection)
            .where('uid', isEqualTo: uid)
            .where(FieldPath.documentId, whereIn: batchIds);

        // Apply date filters
        if (startDate != null) {
          entryQuery = entryQuery.where('entryDate',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
        }
        if (endDate != null) {
          entryQuery = entryQuery.where('entryDate',
              isLessThanOrEqualTo: Timestamp.fromDate(endDate));
        }

        // Apply status filter
        if (statusFilter != null && statusFilter.isNotEmpty) {
          final statusStrings = statusFilter.map((s) => s.name).toList();
          entryQuery = entryQuery.where('status', whereIn: statusStrings);
        }

        // Apply ordering by creation time for proper display order (newest entries first)
        entryQuery = entryQuery.orderBy('createdAt', descending: true);

        final snapshot = await entryQuery.get();

        // Load journal entries with their lines (embedded or separate collection)
        for (final doc in snapshot.docs) {
          try {
            final docData = doc.data() as Map<String, dynamic>;

            // Check if lines are embedded in the document (new atomic transaction format)
            final embeddedLines = docData['lines'] as List<dynamic>?;

            if (embeddedLines != null && embeddedLines.isNotEmpty) {
              // Create journal entry using fromFirestore which handles embedded lines automatically
              final entry = JournalEntryModel.fromFirestore(doc);
              allJournalEntries.add(entry);
            } else {
              // Fallback: Get lines from separate collection (legacy format)
              final linesSnapshot = await _firestore
                  .collection(AppCollection.journalEntryLinesCollection)
                  .where('journalEntryId', isEqualTo: doc.id)
                  .orderBy('createdAt')
                  .get();

              final lines = linesSnapshot.docs
                  .map((lineDoc) =>
                      JournalEntryLineModel.fromFirestore(lineDoc.data()))
                  .toList();

              // Create journal entry with proper Timestamp handling
              final entry = _createJournalEntryFromFirestore(doc, lines);
              allJournalEntries.add(entry);
            }
          } catch (e) {
            log('⚠️ Error loading journal entry ${doc.id}: $e');
            // Continue processing other entries
          }
        }
      }

      // Sort all entries by creation time (descending) for proper display order - newest entries first
      allJournalEntries.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // Debug: Log the first few entries to verify sorting
      log('🔍 Sorted ${allJournalEntries.length} journal entries by creation time (newest first):');
      for (int i = 0; i < allJournalEntries.length && i < 3; i++) {
        final entry = allJournalEntries[i];
        log('   ${i + 1}. ${entry.entryNumber} - ${entry.description} - Created: ${entry.createdAt} - Type: ${entry.entryType.name} - Source: ${entry.sourceTransactionType ?? 'manual'}');
      }

      // Apply pagination to the combined results
      final startIndex = lastDocument != null
          ? 0
          : 0; // TODO: Implement proper cursor-based pagination
      final endIndex = (startIndex + limit).clamp(0, allJournalEntries.length);
      final paginatedEntries = allJournalEntries.sublist(startIndex, endIndex);

      log('Successfully fetched ${paginatedEntries.length} journal entries for account with ${paginatedEntries.fold(0, (total, entry) => total + entry.lines.length)} total lines');
      return paginatedEntries;
    } catch (e) {
      log('Error fetching journal entries for account: $e');
      rethrow;
    }
  }

  /// Calculate account balance from posted journal entries
  Future<double> calculateAccountBalance(String accountId) async {
    log('Calculating account balance for: $accountId');
    try {
      // Get account information to determine account type
      final accountDoc = await _firestore
          .collection(AppCollection.chartOfAccountsCollection)
          .where('id', isEqualTo: accountId)
          .limit(1)
          .get();

      if (accountDoc.docs.isEmpty) {
        log('Account not found: $accountId');
        return 0.0;
      }

      final accountData = accountDoc.docs.first.data();
      final account = ChartOfAccountsModel.fromJson(accountData);

      // Get all posted journal entry lines for this account
      final linesSnapshot = await _firestore
          .collection(AppCollection.journalEntryLinesCollection)
          .where('accountId', isEqualTo: accountId)
          .get();

      double totalDebits = 0.0;
      double totalCredits = 0.0;

      for (final lineDoc in linesSnapshot.docs) {
        final lineData = lineDoc.data();

        // Check if the parent journal entry is posted
        final journalEntryId = lineData['journalEntryId'];
        final entryDoc = await _firestore
            .collection(AppCollection.journalEntriesCollection)
            .doc(journalEntryId)
            .get();

        if (entryDoc.exists) {
          final entryData = entryDoc.data() as Map<String, dynamic>;
          final status =
              JournalEntryStatus.fromString(entryData['status'] ?? 'draft');

          // Only include posted entries in balance calculation
          if (status == JournalEntryStatus.posted) {
            final line = JournalEntryLineModel.fromFirestore(lineData);
            totalDebits += line.debitAmount;
            totalCredits += line.creditAmount;
          }
        }
      }

      // Calculate balance based on account type
      double balance;
      if (_isDebitAccount(account.accountType)) {
        // For asset and expense accounts: debit increases, credit decreases
        balance = totalDebits - totalCredits;
      } else {
        // For liability, equity, and revenue accounts: credit increases, debit decreases
        balance = totalCredits - totalDebits;
      }

      log('Account balance calculated: Debits=$totalDebits, Credits=$totalCredits, Balance=$balance (${account.accountType.name})');
      return balance;
    } catch (e) {
      log('Error calculating account balance: $e');
      rethrow;
    }
  }

  /// Check if account type is a debit account (assets, expenses)
  bool _isDebitAccount(AccountType accountType) {
    switch (accountType.category) {
      case AccountCategory.assets:
      case AccountCategory.expenses:
        return true;
      case AccountCategory.liabilities:
      case AccountCategory.equity:
      case AccountCategory.revenue:
        return false;
    }
  }

  /// Helper method to create JournalEntryModel from Firestore document with backward-compatible Timestamp handling
  JournalEntryModel _createJournalEntryFromFirestore(
      DocumentSnapshot doc, List<JournalEntryLineModel> lines) {
    final data = doc.data() as Map<String, dynamic>;

    return JournalEntryModel(
      id: doc.id,
      entryNumber: data['entryNumber'] ?? '',
      entryDate: _parseDateTime(data['entryDate']),
      description: data['description'] ?? '',
      entryType: JournalEntryType.fromString(data['entryType'] ?? 'manual'),
      status: JournalEntryStatus.fromString(data['status'] ?? 'draft'),
      lines: lines, // Use the separately loaded lines
      totalDebits: (data['totalDebits'] ?? 0.0).toDouble(),
      totalCredits: (data['totalCredits'] ?? 0.0).toDouble(),
      referenceNumber: data['referenceNumber'],
      sourceTransactionId: data['sourceTransactionId'],
      sourceTransactionType: data['sourceTransactionType'],
      reversalOfEntryId: data['reversalOfEntryId'],
      createdAt: _parseDateTime(data['createdAt']),
      updatedAt:
          data['updatedAt'] != null ? _parseDateTime(data['updatedAt']) : null,
      createdBy: data['createdBy'] ?? '',
      uid: data['uid'] ?? '',
    );
  }

  /// Helper method to parse DateTime from either Timestamp or int (backward compatibility)
  DateTime _parseDateTime(dynamic value) {
    if (value == null) return DateTime.now();

    if (value is Timestamp) {
      // New format: Firestore Timestamp
      return value.toDate();
    } else if (value is int) {
      // Old format: milliseconds since epoch
      return DateTime.fromMillisecondsSinceEpoch(value);
    } else {
      // Fallback
      log('⚠️ Unexpected date format: ${value.runtimeType} - $value');
      return DateTime.now();
    }
  }

  /// Stream to listen for real-time updates to journal entries
  Stream<List<JournalEntryModel>> listenToJournalEntries() {
    try {
      return _firestore
          .collection(AppCollection.journalEntriesCollection)
          .where('uid', isEqualTo: _uid)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .asyncMap((snapshot) async {
        final entries = <JournalEntryModel>[];

        for (final doc in snapshot.docs) {
          // Get lines for this journal entry
          final linesSnapshot = await _firestore
              .collection(AppCollection.journalEntryLinesCollection)
              .where('journalEntryId', isEqualTo: doc.id)
              .orderBy('createdAt')
              .get();

          final lines = linesSnapshot.docs
              .map((lineDoc) =>
                  JournalEntryLineModel.fromFirestore(lineDoc.data()))
              .toList();

          // Create journal entry with proper Timestamp handling
          final entry = _createJournalEntryFromFirestore(doc, lines);

          entries.add(entry);
        }

        return entries;
      });
    } catch (e) {
      log('Error listening to journal entries: $e', error: e);
      return Stream.value([]);
    }
  }
}
