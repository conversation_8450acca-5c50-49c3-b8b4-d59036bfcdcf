import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/finance/deposit_firebase_service.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/core/services/deposit_accounting_hook_service.dart';

abstract class DepositRepository {
  Future<Either<FailureObj, List<DepositModel>>> getDeposits();
  // Future<Either<FailureObj, SuccessObj>> createDeposit(DepositModel deposit,
  //     {File? file, Uint8List? fileBytes, String? fileName});
  Future<Either<FailureObj, SuccessObj>> createDeposit(DepositModel deposit);
  Future<Either<FailureObj, SuccessObj>> deleteDeposit(
      String depositId, String accountId, double amount);
  Stream<List<DepositModel>> listenToDeposits();
}

class DepositRepositoryImpl implements DepositRepository {
  final DepositFirebaseService _firebaseService;
  final DepositAccountingHookService _hookService;

  DepositRepositoryImpl(this._firebaseService)
      : _hookService = DepositAccountingHookService();

  // Future<Either<FailureObj, SuccessObj>> createDeposit(DepositModel deposit,
  //     {File? file, Uint8List? fileBytes, String? fileName}) async {
  @override
  Future<Either<FailureObj, SuccessObj>> createDeposit(
      DepositModel deposit) async {
    try {
      log('Creating deposit in repository: ${deposit.accountName}');

      // Check if this deposit uses Chart of Accounts
      if (deposit.usesChartOfAccounts) {
        log('Creating Chart of Accounts deposit with atomic transaction');
        return await _createDepositWithAtomicTransaction(deposit);
      } else {
        log('Creating legacy deposit');
        return await _createLegacyDeposit(deposit);
      }
    } catch (e) {
      log('Error creating deposit: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  /// Create Chart of Accounts deposit with atomic transaction
  Future<Either<FailureObj, SuccessObj>> _createDepositWithAtomicTransaction(
      DepositModel deposit) async {
    try {
      log('Starting atomic deposit creation for Chart of Accounts');

      // First, validate that journal entry can be created
      final validation = await _hookService.validateDeposit(deposit);
      if (!validation.isValid) {
        log('Deposit validation failed: ${validation.issuesText}');
        return Left(FailureObj(
          code: AppStrings.errorS,
          message: 'Validation failed: ${validation.issuesText}',
        ));
      }

      // Check if journal entries already exist (prevent duplicates)
      final hasExisting =
          await _hookService.hasJournalEntries(deposit.id, deposit.uid);
      if (hasExisting) {
        log('Journal entries already exist for deposit: ${deposit.id}');
        return Left(FailureObj(
          code: AppStrings.errorS,
          message: 'Journal entry already exists for this deposit',
        ));
      }

      // Create deposit and journal entry atomically
      await _firebaseService.createDeposit(deposit);
      log('Deposit created successfully, now creating journal entry');

      // Create journal entry - if this fails, we need to rollback
      try {
        await _hookService.onDepositCreated(deposit);
        log('Journal entry created successfully');
      } catch (journalError) {
        log('Journal entry creation failed, rolling back deposit: $journalError');

        // Rollback: Delete the deposit that was just created
        try {
          await _firebaseService.deleteDeposit(deposit.id);
          log('Successfully rolled back deposit creation');
        } catch (rollbackError) {
          log('Failed to rollback deposit: $rollbackError');
          // Return error indicating both creation and rollback failed
          return Left(FailureObj(
            code: AppStrings.errorS,
            message:
                'Journal entry creation failed and rollback failed. Please contact support. Original error: $journalError',
          ));
        }

        // Return the original journal creation error
        return Left(FailureObj(
          code: AppStrings.errorS,
          message: 'Failed to create journal entry: $journalError',
        ));
      }

      return Right(SuccessObj(message: 'Deposit created successfully'));
    } catch (e) {
      log('Error in atomic deposit creation: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  /// Create legacy deposit (backward compatibility)
  Future<Either<FailureObj, SuccessObj>> _createLegacyDeposit(
      DepositModel deposit) async {
    try {
      // Create the deposit first
      await _firebaseService.createDeposit(deposit);

      // Trigger accounting hook for journal entry generation (optional for legacy)
      await _hookService.onDepositCreated(deposit);

      return Right(SuccessObj(message: 'Deposit created successfully'));
    } catch (e) {
      log('Error creating legacy deposit: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<DepositModel>>> getDeposits() async {
    try {
      log('Fetching all deposits');
      final deposits = await _firebaseService.getDeposits();
      return Right(deposits);
    } catch (e) {
      log('Error fetching deposits: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteDeposit(
      String depositId, String accountId, double amount) async {
    try {
      log('Deleting deposit: $depositId');
      await _firebaseService.deleteDeposit(
        depositId,
      );
      return Right(SuccessObj(message: 'Deposit deleted successfully'));
    } catch (e) {
      log('Error deleting deposit: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<DepositModel>> listenToDeposits() {
    return _firebaseService.listenToDeposits();
  }
}
