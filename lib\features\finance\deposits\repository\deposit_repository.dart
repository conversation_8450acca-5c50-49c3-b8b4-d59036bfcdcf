import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:logestics/core/shared_services/failure_obj.dart';
import 'package:logestics/core/shared_services/success_obj.dart';
import 'package:logestics/core/utils/app_constants/texts/app_strings.dart';
import 'package:logestics/firebase_service/finance/deposit_firebase_service.dart';
import 'package:logestics/models/finance/deposit_model.dart';
import 'package:logestics/core/services/deposit_accounting_hook_service.dart';

abstract class DepositRepository {
  Future<Either<FailureObj, List<DepositModel>>> getDeposits();
  // Future<Either<FailureObj, SuccessObj>> createDeposit(DepositModel deposit,
  //     {File? file, Uint8List? fileBytes, String? fileName});
  Future<Either<FailureObj, SuccessObj>> createDeposit(DepositModel deposit);
  Future<Either<FailureObj, SuccessObj>> deleteDeposit(
      String depositId, String accountId, double amount);
  Stream<List<DepositModel>> listenToDeposits();
}

class DepositRepositoryImpl implements DepositRepository {
  final DepositFirebaseService _firebaseService;
  final DepositAccountingHookService _hookService;

  DepositRepositoryImpl(this._firebaseService)
      : _hookService = DepositAccountingHookService();

  // Future<Either<FailureObj, SuccessObj>> createDeposit(DepositModel deposit,
  //     {File? file, Uint8List? fileBytes, String? fileName}) async {
  @override
  Future<Either<FailureObj, SuccessObj>> createDeposit(
      DepositModel deposit) async {
    try {
      log('Creating deposit in repository: ${deposit.accountName}');

      // Create the deposit first
      await _firebaseService.createDeposit(deposit);

      // Trigger accounting hook for journal entry generation
      await _hookService.onDepositCreated(deposit);

      return Right(SuccessObj(message: 'Deposit created successfully'));
    } catch (e) {
      log('Error creating deposit: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, List<DepositModel>>> getDeposits() async {
    try {
      log('Fetching all deposits');
      final deposits = await _firebaseService.getDeposits();
      return Right(deposits);
    } catch (e) {
      log('Error fetching deposits: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Future<Either<FailureObj, SuccessObj>> deleteDeposit(
      String depositId, String accountId, double amount) async {
    try {
      log('Deleting deposit: $depositId');
      await _firebaseService.deleteDeposit(
        depositId,
      );
      return Right(SuccessObj(message: 'Deposit deleted successfully'));
    } catch (e) {
      log('Error deleting deposit: $e');
      return Left(FailureObj(code: AppStrings.errorS, message: e.toString()));
    }
  }

  @override
  Stream<List<DepositModel>> listenToDeposits() {
    return _firebaseService.listenToDeposits();
  }
}
